@extends('layouts.vendor')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')
@section('page-subtitle', 'Welcome back! Here\'s what\'s happening with your store today.')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-dashboard.css') }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/mobile-dashboard.css') }}" media="(max-width: 768px)">
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Welcome Back Section -->
    <div class="welcome-section">
        <div class="welcome-content">
            <h1 class="welcome-title">
                Good {{ date('H') < 12 ? 'morning' : (date('H') < 18 ? 'afternoon' : 'evening') }}, {{ Auth::user()->name ?? 'Vendor' }}! 👋
            </h1>
            <p class="welcome-subtitle">Here's what's happening with your store today</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stats-card sales">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+0%</span>
                </div>
            </div>
            <div class="stats-value">₹0</div>
            <div class="stats-label">Total Sales</div>
        </div>

        <div class="stats-card orders">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+0%</span>
                </div>
            </div>
            <div class="stats-value">0</div>
            <div class="stats-label">Total Orders</div>
        </div>

        <div class="stats-card products">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+0%</span>
                </div>
            </div>
            <div class="stats-value">0</div>
            <div class="stats-label">Total Products</div>
        </div>

        <div class="stats-card customers">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+0%</span>
                </div>
            </div>
            <div class="stats-value">0</div>
            <div class="stats-label">Total Customers</div>
        </div>
    </div>

<!-- Recent Orders Section -->
<div class="dashboard-section">
    <div class="orders-section">
        <div class="section-header">
            <h2 class="section-title">Recent Orders</h2>
            <a href="{{ route('vendor.orders') }}" class="section-action">View All</a>
        </div>

        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <h3>No Orders Yet</h3>
            <p>Your orders will appear here once customers start purchasing from your store.</p>
            <a href="{{ route('vendor.products.index') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Add More Products
            </a>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="dashboard-section">
    <div class="quick-actions-section">
        <div class="section-header">
            <h2 class="section-title">Quick Actions</h2>
        </div>

        <div class="quick-actions-grid">
            <!-- Onboarding step and route removed -->

            <a href="{{ route('vendor.orders') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Manage Orders</div>
                    <div class="quick-action-desc">View and process orders</div>
                </div>
            </a>

            <a href="{{ route('vendor.customers') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">View Customers</div>
                    <div class="quick-action-desc">Manage customer relationships</div>
                </div>
            </a>

            <a href="{{ route('vendor.store-settings') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Store Settings</div>
                    <div class="quick-action-desc">Configure your store details</div>
                </div>
            </a>

            <a href="{{ route('vendor.payment-settings') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Payment Settings</div>
                    <div class="quick-action-desc">Manage payment options</div>
                </div>
            </a>
        </div>
    </div>
</div>
</div>

<!-- ===== MOBILE NATIVE APP INTERFACE ===== -->
<div class="mobile-dashboard-app">

    <!-- Mobile Stats Cards -->
    <div class="mobile-stats-section">
        <h2 class="mobile-stats-title">Overview</h2>
        <div class="mobile-stats-grid">
            <div class="mobile-stat-card">
                <div class="mobile-stat-number">₹0</div>
                <div class="mobile-stat-label">Total Sales</div>
                <i class="mobile-stat-icon fas fa-rupee-sign"></i>
            </div>

            <div class="mobile-stat-card orders">
                <div class="mobile-stat-number">0</div>
                <div class="mobile-stat-label">Total Orders</div>
                <i class="mobile-stat-icon fas fa-shopping-bag"></i>
            </div>

            <div class="mobile-stat-card products">
                <div class="mobile-stat-number">0</div>
                <div class="mobile-stat-label">Total Products</div>
                <i class="mobile-stat-icon fas fa-box"></i>
            </div>

            <div class="mobile-stat-card customers">
                <div class="mobile-stat-number">0</div>
                <div class="mobile-stat-label">Total Customers</div>
                <i class="mobile-stat-icon fas fa-users"></i>
            </div>
        </div>
    </div>

    <!-- Mobile Quick Actions -->
    <div class="mobile-quick-actions">
        <h3 class="mobile-quick-actions-title">Quick Actions</h3>
        <div class="mobile-quick-actions-grid">
            <a href="{{ route('vendor.orders') }}" class="mobile-quick-action-card">
                <div class="mobile-quick-action-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="mobile-quick-action-content">
                    <div class="mobile-quick-action-title">Manage Orders</div>
                    <div class="mobile-quick-action-desc">View and process orders</div>
                </div>
            </a>

            <a href="{{ route('vendor.customers') }}" class="mobile-quick-action-card">
                <div class="mobile-quick-action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="mobile-quick-action-content">
                    <div class="mobile-quick-action-title">View Customers</div>
                    <div class="mobile-quick-action-desc">Manage customer relationships</div>
                </div>
            </a>

            <a href="{{ route('vendor.store-settings') }}" class="mobile-quick-action-card">
                <div class="mobile-quick-action-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="mobile-quick-action-content">
                    <div class="mobile-quick-action-title">Store Settings</div>
                    <div class="mobile-quick-action-desc">Configure your store details</div>
                </div>
            </a>

            <a href="{{ route('vendor.payment-settings') }}" class="mobile-quick-action-card">
                <div class="mobile-quick-action-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="mobile-quick-action-content">
                    <div class="mobile-quick-action-title">Payment Settings</div>
                    <div class="mobile-quick-action-desc">Manage payment options</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Mobile Recent Orders -->
    <div class="mobile-recent-orders">
        <div class="mobile-section-header">
            <h3 class="mobile-section-title">Recent Orders</h3>
            <a href="{{ route('vendor.orders') }}" class="mobile-section-action">View All</a>
        </div>

        <div class="mobile-empty-state">
            <div class="mobile-empty-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <h3 class="mobile-empty-title">No Orders Yet</h3>
            <p class="mobile-empty-description">
                Your orders will appear here once customers start purchasing from your store.
            </p>
            <a href="{{ route('vendor.products.index') }}" class="mobile-empty-action">
                <i class="fas fa-plus"></i>
                Add More Products
            </a>
        </div>
    </div>

    <!-- Mobile Safe Area -->
    <div class="mobile-safe-bottom"></div>

</div>

@endsection

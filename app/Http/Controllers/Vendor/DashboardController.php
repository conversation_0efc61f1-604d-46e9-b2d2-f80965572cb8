<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the vendor dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        // Check if user has completed onboarding
        $user = auth()->user();
        $store = $user->store;
        
        
        // Check if store has at least one product (onboarding completion)
        // Onboarding redirect temporarily disabled
        // if (!$store) {
        //     return redirect()->route('vendor.onboarding');
        // }
        // if ($store->products()->count() === 0) {
        //     return redirect()->route('vendor.onboarding');
        // }
        
        return view('vendor.dashboard');
    }
    
    /**
     * Display the vendor onboarding.
     *
     * @return \Illuminate\View\View
     */
    public function onboarding()
    {
        // Check if onboarding is complete
        $user = auth()->user();
        $store = $user->store;
        
        if ($store && $store->products()->count() > 0) {
            return redirect()->route('vendor.dashboard');
        }
        
        return view('vendor.onboarding');
    }

    public function analytics()
    {
        return view('vendor.analytics');
    }

    public function customers()
    {
        return view('vendor.customers');
    }

    public function discounts()
    {
        return view('vendor.discounts');
    }

    public function inventory()
    {
        return view('vendor.inventory');
    }

    public function marketing()
    {
        return view('vendor.marketing');
    }

    public function orders()
    {
        return view('vendor.orders');
    }

    public function products()
    {
        return view('vendor.products.index');
    }

    public function profile()
    {
        return view('vendor.profile');
    }

    public function reports()
    {
        return view('vendor.reports');
    }

    public function reviews()
    {
        return view('vendor.reviews');
    }

    public function settings(Request $request)
    {
        $section = $request->query('section', 'store');

        $viewMap = [
            'store' => 'vendor.store-settings',
            'payments' => 'vendor.payments',
            // Add other sections here as they are created
            // 'profile' => 'vendor.profile',
            // 'security' => 'vendor.security',
            // 'notifications' => 'vendor.notifications',
        ];

        if (array_key_exists($section, $viewMap)) {
            return view($viewMap[$section]);
        }

        // Fallback to the main store settings page if section is invalid
        return view('vendor.store-settings');
    }

    public function settingsMobile()
    {
        return view('vendor.settings.settings-mobile');
    }

    public function shipping()
    {
        return view('vendor.shipping');
    }

    public function staff()
    {
        return view('vendor.staff');
    }

    public function securitySettings()
    {
        return view('vendor.security-settings');
    }

    public function paymentSettings()
    {
        return view('vendor.payments');
    }

    public function storeSettings()
    {
        return view('vendor.store-settings');
    }

    public function support()
    {
        return view('vendor.support');
    }

    public function chatFlowBuilder()
    {
        return view('vendor.chat-flow.builder');
    }

    public function chatFlowList()
    {
        return view('vendor.chat-flow.list');
    }
    
    /**
     * Save store data during onboarding
     */
    public function saveStore(Request $request)
    {
        try {
            $user = auth()->user();
            
            $request->validate([
            // Onboarding temporarily disabled
            // if (!$store) {
            //     return redirect()->route('vendor.onboarding');
            // }
            // if ($store->products()->count() === 0) {
            //     return redirect()->route('vendor.onboarding');
            // }
                'contactEmail' => 'nullable|email',
                'address' => 'nullable|string',
                'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'facebook_url' => 'nullable|url',
                'instagram_url' => 'nullable|url',
                'youtube_url' => 'nullable|url'
            ]);
            
            // Store data
            $storeData = [
                'name' => $request->name,
                'description' => $request->description,
                'businessCategory' => $request->businessCategory,
                'businessSubcategory' => $request->businessSubcategory,
                'businessType' => $request->businessType,
                'productType' => $request->productType,
                'contactPhone' => $request->contactPhone,
                'contactEmail' => $request->contactEmail,
                'address' => $request->address,
                'facebook_url' => $request->facebook_url,
                'instagram_url' => $request->instagram_url,
                'youtube_url' => $request->youtube_url,
                'storeUrl' => \Str::slug($request->name),
                'isActive' => true
            ];
            
            // Handle logo upload if provided
            if ($request->hasFile('logo')) {
                $logoFile = $request->file('logo');
                $logoFileName = time() . '_' . \Str::slug($request->name) . '.' . $logoFile->getClientOriginalExtension();
                
                // Store the logo in public/uploads/store_logos directory
                $logoPath = $logoFile->storeAs('public/uploads/store_logos', $logoFileName);
                
                // Save the path to the logo in the database (remove 'public/' from the beginning)
                $storeData['logo'] = str_replace('public/', '', $logoPath);
            }
            
            $store = $user->store()->create($storeData);
            
            return response()->json(['success' => true, 'store' => $store]);
        } catch (\Exception $e) {
            \Log::error('Store creation error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'An error occurred while creating your store'], 500);
        }
    }
    
    /**
     * Save product data during onboarding
     */
    public function saveProduct(Request $request)
    {
        try {
            $user = auth()->user();
            $store = $user->store;
            
            if (!$store) {
                return response()->json(['success' => false, 'error' => 'Store not found'], 404);
            }
            
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'stock' => 'nullable|integer|min:0',
                'status' => 'nullable|string'
            ]);
            
            // Create product with simplified fields
            // Stock = 0 means unlimited stock
            $product = $store->products()->create([
                'name' => $request->name,
                'description' => $request->description,
                'category' => $request->category,
                'price' => $request->price,
                'stock_quantity' => $request->stock ?? 0, // 0 means unlimited
                'status' => $request->status ?? 'published',
                'user_id' => $user->id,
                'is_active' => true
            ]);
            
            return response()->json(['success' => true, 'product' => $product]);
        } catch (\Illuminate\Database\QueryException $e) {
            // Log the actual database error for debugging
            \Log::error('Product creation error: ' . $e->getMessage());
            
            // Check if it's a column doesn't exist error
            if (str_contains($e->getMessage(), 'Unknown column')) {
                return response()->json(['success' => false, 'error' => 'Database schema mismatch. Please contact support.'], 500);
            }
            
            return response()->json(['success' => false, 'error' => 'Failed to create product due to database error'], 500);
        } catch (\Exception $e) {
            \Log::error('Product creation error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'An unexpected error occurred'], 500);
        }
    }
}

<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fresh Mart - आपका भरोसेमंद किराना स्टोर</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Noto Sans Devanagari', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Desktop Header */
        .desktop-header {
            background: #ffffff;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .store-info h1 {
            font-size: 24px;
            color: #27ae60;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .store-info p {
            font-size: 14px;
            color: #7f8c8d;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box input {
            padding: 12px 45px 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            width: 300px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            border-color: #27ae60;
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #219a52;
        }

        .contact-info {
            text-align: right;
        }

        .contact-info .phone {
            font-size: 18px;
            font-weight: 600;
            color: #27ae60;
            text-decoration: none;
        }

        .contact-info .timing {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(39, 174, 96, 0.3);
            display: none;
        }

        .mobile-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mobile-logo-icon {
            width: 35px;
            height: 35px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .mobile-store-name {
            font-size: 18px;
            font-weight: 600;
        }

        .mobile-actions {
            display: flex;
            gap: 15px;
        }

        .mobile-action-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mobile-action-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 80px 0 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 20px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 15px 25px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .hero-feature i {
            font-size: 20px;
        }

        /* Product Categories */
        .categories-section {
            padding: 60px 0;
            background: #ffffff;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 50px;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
        }

        .category-card {
            background: #ffffff;
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #27ae60;
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            color: white;
        }

        .category-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .category-count {
            font-size: 14px;
            color: #7f8c8d;
        }

        /* Offers Section */
        .offers-section {
            padding: 60px 0;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            text-align: center;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .offer-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .offer-badge {
            background: #e74c3c;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }

        .offer-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .offer-description {
            font-size: 16px;
            opacity: 0.9;
        }
        /* Product Catalog */
        .catalog-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .product-card {
            background: #ffffff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #7f8c8d;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #27ae60;
            margin-bottom: 10px;
        }

        .product-description {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .add-to-cart-btn {
            width: 100%;
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-to-cart-btn:hover {
            background: #219a52;
        }

        /* Gallery Section */
        .gallery-section {
            padding: 60px 0;
            background: #ffffff;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .gallery-item {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        .gallery-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        /* Store Locator */
        .locator-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .locator-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .store-details {
            background: #ffffff;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
        }

        .store-detail-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .detail-icon {
            width: 40px;
            height: 40px;
            background: #27ae60;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .map-placeholder {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            border-radius: 15px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        /* Mobile Bottom Menu */
        .mobile-bottom-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #ffffff;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #7f8c8d;
            text-decoration: none;
            font-size: 12px;
            padding: 8px;
            transition: all 0.3s ease;
        }

        .menu-item.active,
        .menu-item:hover {
            color: #27ae60;
        }

        .menu-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* Chatbot Button */
        .chatbot-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            animation: pulse 2s infinite;
        }

        .chatbot-button:hover {
            transform: scale(1.1);
        }

        @keyframes pulse {
            0% { box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4); }
            50% { box-shadow: 0 4px 30px rgba(39, 174, 96, 0.6); }
            100% { box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4); }
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-section h3 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #27ae60;
        }

        .footer-section p,
        .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            margin-bottom: 10px;
            display: block;
        }

        .footer-section a:hover {
            color: #27ae60;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .desktop-header { display: none; }
            .mobile-header { display: block; }
            .mobile-bottom-menu { display: block; }

            body { padding-top: 70px; }

            .hero-title { font-size: 32px; }
            .hero-features { flex-direction: column; gap: 15px; }
            .hero-feature { justify-content: center; }

            .categories-grid { grid-template-columns: repeat(2, 1fr); gap: 15px; }
            .category-card { padding: 20px 15px; }
            .category-icon { width: 60px; height: 60px; font-size: 24px; }

            .offers-grid { grid-template-columns: 1fr; gap: 20px; }
            .products-grid { grid-template-columns: repeat(2, 1fr); gap: 15px; }
            .gallery-grid { grid-template-columns: repeat(2, 1fr); gap: 15px; }

            .locator-content { grid-template-columns: 1fr; gap: 20px; }
            .store-details { padding: 20px; }

            .search-box input { width: 200px; }
            .chatbot-button { bottom: 90px; }
        }

        @media (max-width: 480px) {
            .categories-grid { grid-template-columns: 1fr; }
            .products-grid { grid-template-columns: 1fr; }
            .gallery-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Desktop Header -->
    <header class="desktop-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">FM</div>
                    <div class="store-info">
                        <h1>Fresh Mart</h1>
                        <p>आपका भरोसेमंद किराना स्टोर</p>
                    </div>
                </div>

                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" placeholder="उत्पाद खोजें...">
                        <button class="search-btn"><i class="fas fa-search"></i></button>
                    </div>

                    <div class="contact-info">
                        <a href="tel:+919876543210" class="phone">
                            <i class="fas fa-phone"></i> +91 98765 43210
                        </a>
                        <div class="timing">सुबह 7:00 - रात 10:00</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">
                <div class="mobile-logo-icon">FM</div>
                <div class="mobile-store-name">Fresh Mart</div>
            </div>
            <div class="mobile-actions">
                <button class="mobile-action-btn"><i class="fas fa-search"></i></button>
                <button class="mobile-action-btn"><i class="fas fa-phone"></i></button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Fresh Mart में आपका स्वागत है</h1>
                <p class="hero-subtitle">ताज़ा सामान, बेहतरीन गुणवत्ता, सबसे अच्छे दाम</p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-truck"></i>
                        <span>फ्री होम डिलीवरी</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-clock"></i>
                        <span>30 मिनट में डिलीवरी</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>100% गुणवत्ता की गारंटी</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories -->
    <section class="categories-section">
        <div class="container">
            <h2 class="section-title">उत्पाद श्रेणियां</h2>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-apple-alt"></i></div>
                    <div class="category-name">फल और सब्जियां</div>
                    <div class="category-count">50+ उत्पाद</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-bread-slice"></i></div>
                    <div class="category-name">अनाज और दालें</div>
                    <div class="category-count">30+ उत्पाद</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-cookie-bite"></i></div>
                    <div class="category-name">स्नैक्स और बिस्कुट</div>
                    <div class="category-count">40+ उत्पाद</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-wine-bottle"></i></div>
                    <div class="category-name">पेय पदार्थ</div>
                    <div class="category-count">25+ उत्पाद</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-pump-soap"></i></div>
                    <div class="category-name">घरेलू सामान</div>
                    <div class="category-count">35+ उत्पाद</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-baby"></i></div>
                    <div class="category-name">बेबी केयर</div>
                    <div class="category-count">20+ उत्पाद</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Offers Section -->
    <section class="offers-section">
        <div class="container">
            <h2 class="section-title">आज के विशेष ऑफर</h2>
            <div class="offers-grid">
                <div class="offer-card">
                    <div class="offer-badge">50% छूट</div>
                    <h3 class="offer-title">फल और सब्जियों पर</h3>
                    <p class="offer-description">ताज़े फल और सब्जियों पर आज ही 50% तक की छूट पाएं</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">फ्री डिलीवरी</div>
                    <h3 class="offer-title">₹500 से ऊपर के ऑर्डर पर</h3>
                    <p class="offer-description">₹500 या उससे अधिक की खरीदारी पर फ्री होम डिलीवरी</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">बाय 2 गेट 1</div>
                    <h3 class="offer-title">चुनिंदा उत्पादों पर</h3>
                    <p class="offer-description">चुनिंदा उत्पादों पर 2 खरीदें और 1 फ्री पाएं</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Catalog -->
    <section class="catalog-section">
        <div class="container">
            <h2 class="section-title">लोकप्रिय उत्पाद</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-apple-alt"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">ताज़े सेब</h3>
                        <div class="product-price">₹120/किलो</div>
                        <p class="product-description">कश्मीरी ताज़े और मीठे सेब</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-carrot"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">गाजर</h3>
                        <div class="product-price">₹40/किलो</div>
                        <p class="product-description">ताज़ी और मीठी गाजर</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-bread-slice"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">ब्रेड</h3>
                        <div class="product-price">₹25/पैकेट</div>
                        <p class="product-description">फ्रेश व्हाइट ब्रेड</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-wine-bottle"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">दूध</h3>
                        <div class="product-price">₹60/लीटर</div>
                        <p class="product-description">फुल क्रीम फ्रेश दूध</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-cookie-bite"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">बिस्कुट</h3>
                        <div class="product-price">₹30/पैकेट</div>
                        <p class="product-description">क्रीम बिस्कुट</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image"><i class="fas fa-seedling"></i></div>
                    <div class="product-info">
                        <h3 class="product-name">चावल</h3>
                        <div class="product-price">₹80/किलो</div>
                        <p class="product-description">बासमती चावल</p>
                        <button class="add-to-cart-btn">कार्ट में डालें</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <h2 class="section-title">हमारी दुकान की झलकियां</h2>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-store"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-shopping-cart"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-users"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-truck"></i></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Store Locator -->
    <section class="locator-section">
        <div class="container">
            <h2 class="section-title">हमारी दुकान का पता</h2>
            <div class="locator-content">
                <div class="store-details">
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div>
                            <h4>पता</h4>
                            <p>123, मुख्य बाज़ार, सेक्टर 15<br>गुड़गांव, हरियाणा - 122001</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-phone"></i></div>
                        <div>
                            <h4>फोन नंबर</h4>
                            <p>+91 98765 43210<br>+91 87654 32109</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-clock"></i></div>
                        <div>
                            <h4>खुलने का समय</h4>
                            <p>सुबह 7:00 - रात 10:00<br>सभी दिन खुला</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-envelope"></i></div>
                        <div>
                            <h4>ईमेल</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="map-placeholder">
                    <i class="fas fa-map"></i>
                    <span>Google Map यहाँ होगा</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Bottom Menu -->
    <nav class="mobile-bottom-menu">
        <div class="bottom-menu-items">
            <a href="#" class="menu-item active">
                <div class="menu-icon"><i class="fas fa-home"></i></div>
                <span>होम</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-th-large"></i></div>
                <span>श्रेणी</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-shopping-cart"></i></div>
                <span>कार्ट</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-percent"></i></div>
                <span>ऑफर</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-user"></i></div>
                <span>प्रोफाइल</span>
            </a>
        </div>
    </nav>

    <!-- Chatbot Button -->
    <div class="chatbot-button" onclick="openChat()">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Fresh Mart</h3>
                    <p>आपका भरोसेमंद किराना स्टोर। हम आपको सबसे ताज़ा और बेहतरीन गुणवत्ता का सामान प्रदान करते हैं।</p>
                </div>
                <div class="footer-section">
                    <h3>संपर्क जानकारी</h3>
                    <p><i class="fas fa-map-marker-alt"></i> 123, मुख्य बाज़ार, सेक्टर 15, गुड़गांव</p>
                    <p><i class="fas fa-phone"></i> +91 98765 43210</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h3>सेवाएं</h3>
                    <a href="#">होम डिलीवरी</a>
                    <a href="#">ऑनलाइन ऑर्डर</a>
                    <a href="#">बल्क ऑर्डर</a>
                    <a href="#">कस्टमर सपोर्ट</a>
                </div>
                <div class="footer-section">
                    <h3>खुलने का समय</h3>
                    <p>सोमवार - रविवार</p>
                    <p>सुबह 7:00 - रात 10:00</p>
                    <p>सभी दिन खुला</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fresh Mart. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu functionality
        function toggleMobileMenu() {
            const menu = document.querySelector('.mobile-menu');
            menu.classList.toggle('active');
        }

        // Chatbot functionality
        function openChat() {
            alert('चैटबॉट जल्द ही उपलब्ध होगा! अभी के लिए कृपया +91 98765 43210 पर कॉल करें।');
        }

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productName = this.closest('.product-card').querySelector('.product-name').textContent;
                alert(`${productName} कार्ट में जोड़ दिया गया!`);
            });
        });

        // Category card click functionality
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const categoryName = this.querySelector('.category-name').textContent;
                alert(`${categoryName} श्रेणी में जा रहे हैं...`);
            });
        });

        // Gallery item click functionality
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                alert('गैलरी इमेज को बड़ा करके देखने की सुविधा जल्द ही उपलब्ध होगी!');
            });
        });

        // Search functionality
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchTerm = document.querySelector('.search-box input').value;
            if (searchTerm.trim()) {
                alert(`"${searchTerm}" के लिए खोज रहे हैं...`);
            } else {
                alert('कृपया कुछ खोजें!');
            }
        });

        // Mobile bottom menu active state
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>

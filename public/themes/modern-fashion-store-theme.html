<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StyleHub - Fashion & Footwear Collection</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fafafa;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Desktop Header */
        .desktop-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #2c3e50;
            box-shadow: 0 4px 25px rgba(255, 154, 158, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            font-family: 'Playfair Display', serif;
        }

        .store-info h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 3px;
            font-family: 'Playfair Display', serif;
            color: #e91e63;
        }

        .store-info p {
            font-size: 14px;
            color: #6c757d;
            font-style: italic;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box input {
            padding: 14px 50px 14px 18px;
            border: 2px solid rgba(233, 30, 99, 0.2);
            border-radius: 30px;
            width: 320px;
            font-size: 14px;
            outline: none;
            background: rgba(255,255,255,0.8);
            color: #2c3e50;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            background: rgba(255,255,255,0.95);
            border-color: #e91e63;
            box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            background: #e91e63;
            color: white;
            border: none;
            padding: 10px 14px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #ad1457;
            transform: scale(1.05);
        }

        .contact-info {
            text-align: right;
        }

        .contact-info .phone {
            font-size: 18px;
            font-weight: 600;
            color: #e91e63;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-info .timing {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #2c3e50;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 25px rgba(255, 154, 158, 0.3);
            display: none;
        }

        .mobile-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .mobile-logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(233, 30, 99, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-family: 'Playfair Display', serif;
            color: #e91e63;
        }

        .mobile-store-name {
            font-size: 20px;
            font-weight: 700;
            font-family: 'Playfair Display', serif;
            color: #e91e63;
        }

        .mobile-actions {
            display: flex;
            gap: 15px;
        }

        .mobile-action-btn {
            background: rgba(233, 30, 99, 0.2);
            border: none;
            color: #e91e63;
            padding: 10px;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mobile-action-btn:hover {
            background: rgba(233, 30, 99, 0.3);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #2c3e50;
            padding: 100px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="fashion" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(233,30,99,0.1)"/><circle cx="5" cy="5" r="0.5" fill="rgba(233,30,99,0.1)"/><circle cx="15" cy="15" r="0.5" fill="rgba(233,30,99,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23fashion)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 56px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            font-family: 'Playfair Display', serif;
            color: #e91e63;
        }

        .hero-subtitle {
            font-size: 22px;
            margin-bottom: 40px;
            color: #6c757d;
            font-style: italic;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 50px;
            margin-top: 50px;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255,255,255,0.3);
            padding: 18px 30px;
            border-radius: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.4);
            color: #2c3e50;
        }

        .hero-feature i {
            font-size: 22px;
            color: #e91e63;
        }

        /* Product Categories */
        .categories-section {
            padding: 80px 0;
            background: #ffffff;
        }

        .section-title {
            text-align: center;
            font-size: 42px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 60px;
            font-family: 'Playfair Display', serif;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .category-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-radius: 20px;
            padding: 35px 25px;
            text-align: center;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 154, 158, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .category-card:hover::before {
            left: 100%;
        }

        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(255, 154, 158, 0.2);
            border-color: #ff9a9e;
        }

        .category-icon {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 40px;
            color: #e91e63;
            position: relative;
            z-index: 2;
        }

        .category-name {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            font-family: 'Playfair Display', serif;
        }

        .category-count {
            font-size: 14px;
            color: #6c757d;
            position: relative;
            z-index: 2;
        }

        /* Offers Section */
        .offers-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            color: white;
            text-align: center;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 35px;
            margin-top: 50px;
        }

        .offer-card {
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .offer-card:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
        }

        .offer-badge {
            background: #ffffff;
            color: #e91e63;
            padding: 10px 25px;
            border-radius: 30px;
            font-size: 14px;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 20px;
        }

        .offer-title {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 15px;
            font-family: 'Playfair Display', serif;
        }

        .offer-description {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* Product Catalog */
        .catalog-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .product-card {
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: #e91e63;
            position: relative;
        }

        .product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #e91e63;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .product-info {
            padding: 25px;
        }

        .product-name {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            font-family: 'Playfair Display', serif;
        }

        .product-price {
            font-size: 24px;
            font-weight: 700;
            color: #e91e63;
            margin-bottom: 12px;
        }

        .product-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .add-to-cart-btn {
            width: 100%;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #e91e63;
            border: none;
            padding: 14px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-to-cart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 154, 158, 0.3);
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        /* Gallery Section */
        .gallery-section {
            padding: 80px 0;
            background: #ffffff;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 50px;
        }

        .gallery-item {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        .gallery-image {
            width: 100%;
            height: 220px;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }

        /* Store Locator */
        .locator-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .locator-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin-top: 50px;
        }

        .store-details {
            background: #ffffff;
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }

        .store-detail-item {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .store-detail-item:hover {
            background: #ff9a9e;
            color: white;
            transform: translateX(5px);
        }

        .detail-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e91e63;
            font-size: 20px;
        }

        .store-detail-item:hover .detail-icon {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .map-placeholder {
            background: linear-gradient(135deg, #6c757d, #495057);
            border-radius: 20px;
            height: 450px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
        }

        /* Mobile Bottom Menu */
        .mobile-bottom-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            box-shadow: 0 -4px 25px rgba(255, 154, 158, 0.3);
            z-index: 1000;
            display: none;
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(44, 62, 80, 0.7);
            text-decoration: none;
            font-size: 12px;
            padding: 8px;
            transition: all 0.3s ease;
            border-radius: 10px;
        }

        .menu-item.active,
        .menu-item:hover {
            color: #e91e63;
            background: rgba(255,255,255,0.3);
        }

        .menu-icon {
            font-size: 22px;
            margin-bottom: 5px;
        }

        /* Chatbot Button */
        .chatbot-button {
            position: fixed;
            bottom: 110px;
            right: 25px;
            width: 65px;
            height: 65px;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 26px;
            box-shadow: 0 6px 25px rgba(233, 30, 99, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            animation: fashionPulse 2s infinite;
        }

        .chatbot-button:hover {
            transform: scale(1.1);
        }

        @keyframes fashionPulse {
            0% { box-shadow: 0 6px 25px rgba(233, 30, 99, 0.4); }
            50% { box-shadow: 0 6px 35px rgba(233, 30, 99, 0.6); }
            100% { box-shadow: 0 6px 25px rgba(233, 30, 99, 0.4); }
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 60px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h3 {
            font-size: 22px;
            margin-bottom: 25px;
            color: #ff9a9e;
            font-family: 'Playfair Display', serif;
        }

        .footer-section p,
        .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            margin-bottom: 12px;
            display: block;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #ff9a9e;
            transform: translateX(5px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .desktop-header { display: none; }
            .mobile-header { display: block; }
            .mobile-bottom-menu { display: block; }

            body { padding-top: 80px; }

            .hero-title { font-size: 40px; }
            .hero-features { flex-direction: column; gap: 20px; }
            .hero-feature { justify-content: center; }

            .categories-grid { grid-template-columns: repeat(2, 1fr); gap: 20px; }
            .category-card { padding: 25px 20px; }
            .category-icon { width: 70px; height: 70px; font-size: 30px; }

            .offers-grid { grid-template-columns: 1fr; gap: 25px; }
            .products-grid { grid-template-columns: repeat(2, 1fr); gap: 20px; }
            .gallery-grid { grid-template-columns: repeat(2, 1fr); gap: 20px; }

            .locator-content { grid-template-columns: 1fr; gap: 30px; }
            .store-details { padding: 30px; }

            .search-box input { width: 220px; }
            .chatbot-button { bottom: 100px; }
        }

        @media (max-width: 480px) {
            .categories-grid { grid-template-columns: 1fr; }
            .products-grid { grid-template-columns: 1fr; }
            .gallery-grid { grid-template-columns: 1fr; }
            .hero-title { font-size: 32px; }
            .section-title { font-size: 32px; }
        }
    </style>
</head>
<body>
    <!-- Desktop Header -->
    <header class="desktop-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">SH</div>
                    <div class="store-info">
                        <h1>StyleHub</h1>
                        <p>Fashion & Footwear Collection</p>
                    </div>
                </div>

                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Search for fashion items...">
                        <button class="search-btn"><i class="fas fa-search"></i></button>
                    </div>

                    <div class="contact-info">
                        <a href="tel:+919876543210" class="phone">
                            <i class="fas fa-phone"></i> +91 98765 43210
                        </a>
                        <div class="timing">Mon-Sun: 10:00 AM - 8:00 PM</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">
                <div class="mobile-logo-icon">SH</div>
                <div class="mobile-store-name">StyleHub</div>
            </div>
            <div class="mobile-actions">
                <button class="mobile-action-btn"><i class="fas fa-search"></i></button>
                <button class="mobile-action-btn"><i class="fas fa-phone"></i></button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Welcome to StyleHub</h1>
                <p class="hero-subtitle">Discover the Latest Fashion Trends & Premium Footwear Collection</p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-tshirt"></i>
                        <span>Latest Fashion</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shipping-fast"></i>
                        <span>Free Delivery</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Easy Returns</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories -->
    <section class="categories-section">
        <div class="container">
            <h2 class="section-title">Fashion Categories</h2>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-tshirt"></i></div>
                    <div class="category-name">Men's Clothing</div>
                    <div class="category-count">120+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-female"></i></div>
                    <div class="category-name">Women's Clothing</div>
                    <div class="category-count">200+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-child"></i></div>
                    <div class="category-name">Kids Fashion</div>
                    <div class="category-count">80+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-shoe-prints"></i></div>
                    <div class="category-name">Footwear</div>
                    <div class="category-count">150+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-gem"></i></div>
                    <div class="category-name">Accessories</div>
                    <div class="category-count">60+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-hat-cowboy"></i></div>
                    <div class="category-name">Traditional Wear</div>
                    <div class="category-count">90+ Products</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Offers Section -->
    <section class="offers-section">
        <div class="container">
            <h2 class="section-title">Special Offers</h2>
            <div class="offers-grid">
                <div class="offer-card">
                    <div class="offer-badge">Up to 60% OFF</div>
                    <h3 class="offer-title">Summer Collection Sale</h3>
                    <p class="offer-description">Get amazing discounts on summer clothing and accessories</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">Buy 2 Get 1</div>
                    <h3 class="offer-title">Footwear Festival</h3>
                    <p class="offer-description">Buy any 2 pairs of shoes and get 1 pair absolutely free</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">Free Alteration</div>
                    <h3 class="offer-title">Perfect Fit Guarantee</h3>
                    <p class="offer-description">Free alteration service for all clothing purchases</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Catalog -->
    <section class="catalog-section">
        <div class="container">
            <h2 class="section-title">Featured Products</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-tshirt"></i>
                        <div class="product-badge">New</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Designer Kurta Set</h3>
                        <div class="product-price">₹2,499</div>
                        <p class="product-description">Premium cotton kurta with matching dupatta</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-female"></i>
                        <div class="product-badge">Sale</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Elegant Saree</h3>
                        <div class="product-price">₹3,999</div>
                        <p class="product-description">Beautiful silk saree with intricate embroidery</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-shoe-prints"></i>
                        <div class="product-badge">Hot</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Casual Sneakers</h3>
                        <div class="product-price">₹1,899</div>
                        <p class="product-description">Comfortable and stylish casual sneakers</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-male"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Formal Shirt</h3>
                        <div class="product-price">₹1,299</div>
                        <p class="product-description">Premium cotton formal shirt for office wear</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-child"></i>
                        <div class="product-badge">New</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Kids Party Dress</h3>
                        <div class="product-price">₹899</div>
                        <p class="product-description">Adorable party dress for special occasions</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-gem"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Fashion Jewelry Set</h3>
                        <div class="product-price">₹599</div>
                        <p class="product-description">Elegant jewelry set with earrings and necklace</p>
                        <button class="add-to-cart-btn">Add to Cart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <h2 class="section-title">Fashion Gallery</h2>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-camera-retro"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-palette"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-star"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-heart"></i></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Store Locator -->
    <section class="locator-section">
        <div class="container">
            <h2 class="section-title">Visit Our Boutique</h2>
            <div class="locator-content">
                <div class="store-details">
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div>
                            <h4>Address</h4>
                            <p>Fashion Street, Shop No. 12<br>MG Road, Bangalore - 560001</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-phone"></i></div>
                        <div>
                            <h4>Phone</h4>
                            <p>+91 98765 43210<br>+91 87654 32109</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-clock"></i></div>
                        <div>
                            <h4>Store Hours</h4>
                            <p>Mon-Sun: 10:00 AM - 8:00 PM<br>Open all days</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-envelope"></i></div>
                        <div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="map-placeholder">
                    <i class="fas fa-map"></i>
                    <span>Google Map Integration</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Bottom Menu -->
    <nav class="mobile-bottom-menu">
        <div class="bottom-menu-items">
            <a href="#" class="menu-item active">
                <div class="menu-icon"><i class="fas fa-home"></i></div>
                <span>Home</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-th-large"></i></div>
                <span>Categories</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-shopping-bag"></i></div>
                <span>Cart</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-percent"></i></div>
                <span>Offers</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-user"></i></div>
                <span>Profile</span>
            </a>
        </div>
    </nav>

    <!-- Chatbot Button -->
    <div class="chatbot-button" onclick="openChat()">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>StyleHub</h3>
                    <p>Your premier fashion destination offering the latest trends in clothing and footwear with personalized styling services.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-map-marker-alt"></i> Fashion Street, Shop No. 12, MG Road</p>
                    <p><i class="fas fa-phone"></i> +91 98765 43210</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <a href="#">Personal Styling</a>
                    <a href="#">Custom Tailoring</a>
                    <a href="#">Fashion Consultation</a>
                    <a href="#">Home Delivery</a>
                </div>
                <div class="footer-section">
                    <h3>Store Hours</h3>
                    <p>Monday - Sunday</p>
                    <p>10:00 AM - 8:00 PM</p>
                    <p>Open all days</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 StyleHub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Fashion-specific interactions
        function openChat() {
            alert('Fashion Stylist Chat coming soon! For styling advice, please call +91 98765 43210.');
        }

        // Add to cart with fashion-specific feedback
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productName = this.closest('.product-card').querySelector('.product-name').textContent;
                alert(`${productName} added to your fashion cart! 👗`);

                this.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
                this.style.color = 'white';
                this.textContent = 'Added! ✓';
                setTimeout(() => {
                    this.style.background = '';
                    this.style.color = '';
                    this.textContent = 'Add to Cart';
                }, 2000);
            });
        });

        // Category interactions
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const categoryName = this.querySelector('.category-name').textContent;
                alert(`Exploring ${categoryName} collection... 👕`);
            });
        });

        // Gallery interactions
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                alert('Fashion lookbook coming soon! 📸');
            });
        });

        // Search functionality
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchTerm = document.querySelector('.search-box input').value;
            if (searchTerm.trim()) {
                alert(`Searching for "${searchTerm}" in our fashion collection...`);
            } else {
                alert('Please enter what you\'re looking for!');
            }
        });

        // Mobile menu interactions
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>

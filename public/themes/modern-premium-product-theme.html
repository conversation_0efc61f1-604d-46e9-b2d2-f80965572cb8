<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LuxeCollection - Premium Jewelry & Cosmetics</title>
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fafafa;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Desktop Header */
        .desktop-header {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 50%, #e74c3c 100%);
            color: white;
            box-shadow: 0 4px 25px rgba(142, 68, 173, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .logo {
            width: 65px;
            height: 65px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
            font-family: 'Cormorant Garamond', serif;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .store-info h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 3px;
            font-family: 'Cormorant Garamond', serif;
            letter-spacing: 1px;
        }

        .store-info p {
            font-size: 14px;
            opacity: 0.9;
            font-style: italic;
            letter-spacing: 0.5px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box input {
            padding: 14px 50px 14px 18px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 30px;
            width: 320px;
            font-size: 14px;
            outline: none;
            background: rgba(255,255,255,0.15);
            color: white;
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
        }

        .search-box input::placeholder {
            color: rgba(255,255,255,0.8);
        }

        .search-box input:focus {
            background: rgba(255,255,255,0.25);
            border-color: rgba(255,255,255,0.6);
            box-shadow: 0 0 0 3px rgba(255,255,255,0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            background: #f39c12;
            color: white;
            border: none;
            padding: 10px 14px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #e67e22;
            transform: scale(1.05);
        }

        .contact-info {
            text-align: right;
        }

        .contact-info .phone {
            font-size: 18px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-info .timing {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 50%, #e74c3c 100%);
            color: white;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 25px rgba(142, 68, 173, 0.3);
            display: none;
        }

        .mobile-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .mobile-logo-icon {
            width: 45px;
            height: 45px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-family: 'Cormorant Garamond', serif;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .mobile-store-name {
            font-size: 20px;
            font-weight: 700;
            font-family: 'Cormorant Garamond', serif;
        }

        .mobile-actions {
            display: flex;
            gap: 15px;
        }

        .mobile-action-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .mobile-action-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 50%, #e74c3c 100%);
            color: white;
            padding: 120px 0 100px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="luxury" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><path d="M10,10 L30,10 L30,30 L10,30 Z" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/><circle cx="5" cy="35" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="35" cy="5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23luxury)"/></svg>');
            opacity: 0.4;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 64px;
            font-weight: 700;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-family: 'Cormorant Garamond', serif;
            letter-spacing: 2px;
        }

        .hero-subtitle {
            font-size: 24px;
            margin-bottom: 50px;
            opacity: 0.95;
            font-style: italic;
            letter-spacing: 1px;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 60px;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255,255,255,0.2);
            padding: 20px 35px;
            border-radius: 35px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .hero-feature i {
            font-size: 24px;
            color: #f39c12;
        }

        .hero-feature span {
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* Product Categories */
        .categories-section {
            padding: 100px 0;
            background: #ffffff;
        }

        .section-title {
            text-align: center;
            font-size: 48px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 70px;
            font-family: 'Cormorant Garamond', serif;
            letter-spacing: 1px;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
        }

        .category-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-radius: 25px;
            padding: 45px 30px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.5s ease;
            cursor: pointer;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(142, 68, 173, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .category-card:hover::before {
            left: 100%;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(142, 68, 173, 0.2);
            border-color: #8e44ad;
        }

        .category-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 45px;
            color: white;
            position: relative;
            z-index: 2;
            border: 4px solid rgba(142, 68, 173, 0.2);
        }

        .category-name {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            position: relative;
            z-index: 2;
            font-family: 'Cormorant Garamond', serif;
        }

        .category-count {
            font-size: 14px;
            color: #6c757d;
            position: relative;
            z-index: 2;
            letter-spacing: 0.5px;
        }

        /* Offers Section */
        .offers-section {
            padding: 100px 0;
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            text-align: center;
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .offer-card {
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 50px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.4s ease;
        }

        .offer-card:hover {
            transform: translateY(-8px);
            background: rgba(255,255,255,0.25);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .offer-badge {
            background: #ffffff;
            color: #f39c12;
            padding: 12px 30px;
            border-radius: 35px;
            font-size: 14px;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 25px;
            letter-spacing: 1px;
        }

        .offer-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 18px;
            font-family: 'Cormorant Garamond', serif;
        }

        .offer-description {
            font-size: 16px;
            opacity: 0.95;
            line-height: 1.7;
            letter-spacing: 0.5px;
        }

        /* Product Catalog */
        .catalog-section {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .product-card {
            background: #ffffff;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.5s ease;
            position: relative;
            border: 3px solid transparent;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            border-color: #8e44ad;
        }

        .product-image {
            width: 100%;
            height: 280px;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 70px;
            color: white;
            position: relative;
        }

        .product-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #f39c12;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .product-info {
            padding: 35px;
        }

        .product-name {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            font-family: 'Cormorant Garamond', serif;
        }

        .product-price {
            font-size: 26px;
            font-weight: 700;
            color: #8e44ad;
            margin-bottom: 15px;
        }

        .product-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
            letter-spacing: 0.3px;
        }

        .add-to-cart-btn {
            width: 100%;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            letter-spacing: 0.5px;
        }

        .add-to-cart-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(142, 68, 173, 0.3);
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        /* Gallery Section */
        .gallery-section {
            padding: 100px 0;
            background: #ffffff;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .gallery-item {
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.5s ease;
            cursor: pointer;
            position: relative;
        }

        .gallery-item:hover {
            transform: scale(1.08);
        }

        .gallery-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
        }

        /* Store Locator */
        .locator-section {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .locator-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin-top: 60px;
        }

        .store-details {
            background: #ffffff;
            padding: 60px;
            border-radius: 25px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .store-detail-item {
            display: flex;
            align-items: center;
            gap: 25px;
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 20px;
            transition: all 0.4s ease;
        }

        .store-detail-item:hover {
            background: #8e44ad;
            color: white;
            transform: translateX(8px);
        }

        .detail-icon {
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            border: 3px solid rgba(142, 68, 173, 0.2);
        }

        .store-detail-item:hover .detail-icon {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
        }

        .map-placeholder {
            background: linear-gradient(135deg, #6c757d, #495057);
            border-radius: 25px;
            height: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }

        /* Mobile Bottom Menu */
        .mobile-bottom-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            box-shadow: 0 -6px 30px rgba(142, 68, 173, 0.4);
            z-index: 1000;
            display: none;
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 18px 0;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            font-size: 12px;
            padding: 10px;
            transition: all 0.4s ease;
            border-radius: 12px;
            letter-spacing: 0.3px;
        }

        .menu-item.active,
        .menu-item:hover {
            color: white;
            background: rgba(255,255,255,0.15);
            transform: translateY(-2px);
        }

        .menu-icon {
            font-size: 24px;
            margin-bottom: 6px;
        }

        /* Chatbot Button */
        .chatbot-button {
            position: fixed;
            bottom: 120px;
            right: 30px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            box-shadow: 0 8px 30px rgba(243, 156, 18, 0.4);
            cursor: pointer;
            transition: all 0.4s ease;
            z-index: 1000;
            animation: luxuryPulse 2.5s infinite;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .chatbot-button:hover {
            transform: scale(1.15);
        }

        @keyframes luxuryPulse {
            0% { box-shadow: 0 8px 30px rgba(243, 156, 18, 0.4); }
            50% { box-shadow: 0 8px 40px rgba(243, 156, 18, 0.6); }
            100% { box-shadow: 0 8px 30px rgba(243, 156, 18, 0.4); }
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 80px 0 40px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 50px;
            margin-bottom: 50px;
        }

        .footer-section h3 {
            font-size: 24px;
            margin-bottom: 30px;
            color: #f39c12;
            font-family: 'Cormorant Garamond', serif;
            letter-spacing: 1px;
        }

        .footer-section p,
        .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            margin-bottom: 15px;
            display: block;
            transition: all 0.3s ease;
            letter-spacing: 0.3px;
        }

        .footer-section a:hover {
            color: #f39c12;
            transform: translateX(8px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 40px;
            border-top: 2px solid #34495e;
            color: #95a5a6;
            letter-spacing: 0.5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .desktop-header { display: none; }
            .mobile-header { display: block; }
            .mobile-bottom-menu { display: block; }

            body { padding-top: 85px; }

            .hero-title { font-size: 44px; }
            .hero-features { flex-direction: column; gap: 25px; }
            .hero-feature { justify-content: center; }

            .categories-grid { grid-template-columns: repeat(2, 1fr); gap: 25px; }
            .category-card { padding: 30px 20px; }
            .category-icon { width: 80px; height: 80px; font-size: 35px; }

            .offers-grid { grid-template-columns: 1fr; gap: 30px; }
            .products-grid { grid-template-columns: repeat(2, 1fr); gap: 25px; }
            .gallery-grid { grid-template-columns: repeat(2, 1fr); gap: 25px; }

            .locator-content { grid-template-columns: 1fr; gap: 40px; }
            .store-details { padding: 40px; }

            .search-box input { width: 240px; }
            .chatbot-button { bottom: 110px; }
        }

        @media (max-width: 480px) {
            .categories-grid { grid-template-columns: 1fr; }
            .products-grid { grid-template-columns: 1fr; }
            .gallery-grid { grid-template-columns: 1fr; }
            .hero-title { font-size: 36px; }
            .section-title { font-size: 36px; }
        }
    </style>
</head>
<body>
    <!-- Desktop Header -->
    <header class="desktop-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">LC</div>
                    <div class="store-info">
                        <h1>LuxeCollection</h1>
                        <p>Premium Jewelry & Cosmetics</p>
                    </div>
                </div>

                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Search luxury products...">
                        <button class="search-btn"><i class="fas fa-search"></i></button>
                    </div>

                    <div class="contact-info">
                        <a href="tel:+919876543210" class="phone">
                            <i class="fas fa-phone"></i> +91 98765 43210
                        </a>
                        <div class="timing">Mon-Sun: 10:00 AM - 8:00 PM</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">
                <div class="mobile-logo-icon">LC</div>
                <div class="mobile-store-name">LuxeCollection</div>
            </div>
            <div class="mobile-actions">
                <button class="mobile-action-btn"><i class="fas fa-search"></i></button>
                <button class="mobile-action-btn"><i class="fas fa-phone"></i></button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">LuxeCollection</h1>
                <p class="hero-subtitle">Exquisite Jewelry & Premium Cosmetics for the Discerning You</p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-gem"></i>
                        <span>Authentic Luxury</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-award"></i>
                        <span>Premium Quality</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-crown"></i>
                        <span>Exclusive Collection</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories -->
    <section class="categories-section">
        <div class="container">
            <h2 class="section-title">Luxury Categories</h2>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-gem"></i></div>
                    <div class="category-name">Fine Jewelry</div>
                    <div class="category-count">80+ Exclusive Pieces</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-ring"></i></div>
                    <div class="category-name">Wedding Collection</div>
                    <div class="category-count">50+ Premium Sets</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-palette"></i></div>
                    <div class="category-name">Premium Cosmetics</div>
                    <div class="category-count">120+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-spa"></i></div>
                    <div class="category-name">Skincare Luxury</div>
                    <div class="category-count">60+ Products</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-crown"></i></div>
                    <div class="category-name">Royal Collection</div>
                    <div class="category-count">30+ Exclusive Items</div>
                </div>
                <div class="category-card">
                    <div class="category-icon"><i class="fas fa-gift"></i></div>
                    <div class="category-name">Gift Sets</div>
                    <div class="category-count">40+ Curated Sets</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Offers Section -->
    <section class="offers-section">
        <div class="container">
            <h2 class="section-title">Exclusive Offers</h2>
            <div class="offers-grid">
                <div class="offer-card">
                    <div class="offer-badge">Limited Edition</div>
                    <h3 class="offer-title">Bridal Collection Sale</h3>
                    <p class="offer-description">Exclusive discounts on our premium bridal jewelry and makeup collection</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">VIP Members</div>
                    <h3 class="offer-title">Luxury Skincare Bundle</h3>
                    <p class="offer-description">Complimentary premium skincare set with luxury cosmetics purchase</p>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">Personalized</div>
                    <h3 class="offer-title">Custom Jewelry Design</h3>
                    <p class="offer-description">Free consultation and design service for custom jewelry pieces</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Catalog -->
    <section class="catalog-section">
        <div class="container">
            <h2 class="section-title">Featured Collection</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-gem"></i>
                        <div class="product-badge">Exclusive</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Diamond Necklace Set</h3>
                        <div class="product-price">₹2,49,999</div>
                        <p class="product-description">Exquisite diamond necklace with matching earrings</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-palette"></i>
                        <div class="product-badge">Limited</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Luxury Makeup Kit</h3>
                        <div class="product-price">₹12,999</div>
                        <p class="product-description">Premium makeup collection from international brands</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-ring"></i>
                        <div class="product-badge">Bestseller</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Gold Wedding Rings</h3>
                        <div class="product-price">₹89,999</div>
                        <p class="product-description">Handcrafted 22k gold wedding ring pair</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-spa"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Anti-Aging Serum</h3>
                        <div class="product-price">₹8,999</div>
                        <p class="product-description">Premium anti-aging serum with natural ingredients</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-crown"></i>
                        <div class="product-badge">Royal</div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Royal Tiara</h3>
                        <div class="product-price">₹1,99,999</div>
                        <p class="product-description">Handcrafted royal tiara with precious stones</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">Luxury Gift Set</h3>
                        <div class="product-price">₹15,999</div>
                        <p class="product-description">Curated luxury gift set with jewelry and cosmetics</p>
                        <button class="add-to-cart-btn">Add to Collection</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <h2 class="section-title">Luxury Gallery</h2>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-camera"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-sparkles"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-star"></i></div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-image"><i class="fas fa-heart"></i></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Store Locator -->
    <section class="locator-section">
        <div class="container">
            <h2 class="section-title">Visit Our Luxury Boutique</h2>
            <div class="locator-content">
                <div class="store-details">
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div>
                            <h4>Address</h4>
                            <p>Luxury Mall, 3rd Floor<br>Khan Market, New Delhi - 110003</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-phone"></i></div>
                        <div>
                            <h4>Phone</h4>
                            <p>+91 98765 43210<br>+91 87654 32109</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-clock"></i></div>
                        <div>
                            <h4>Boutique Hours</h4>
                            <p>Mon-Sun: 10:00 AM - 8:00 PM<br>By appointment available</p>
                        </div>
                    </div>
                    <div class="store-detail-item">
                        <div class="detail-icon"><i class="fas fa-envelope"></i></div>
                        <div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="map-placeholder">
                    <i class="fas fa-map"></i>
                    <span>Google Map Integration</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Bottom Menu -->
    <nav class="mobile-bottom-menu">
        <div class="bottom-menu-items">
            <a href="#" class="menu-item active">
                <div class="menu-icon"><i class="fas fa-home"></i></div>
                <span>Home</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-gem"></i></div>
                <span>Collection</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-shopping-bag"></i></div>
                <span>Cart</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-crown"></i></div>
                <span>VIP</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon"><i class="fas fa-user"></i></div>
                <span>Profile</span>
            </a>
        </div>
    </nav>

    <!-- Chatbot Button -->
    <div class="chatbot-button" onclick="openChat()">
        <i class="fas fa-concierge-bell"></i>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LuxeCollection</h3>
                    <p>Your premier destination for luxury jewelry and premium cosmetics. We offer exclusive collections with personalized service and expert consultation.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-map-marker-alt"></i> Luxury Mall, 3rd Floor, Khan Market</p>
                    <p><i class="fas fa-phone"></i> +91 98765 43210</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h3>Luxury Services</h3>
                    <a href="#">Personal Shopping</a>
                    <a href="#">Custom Jewelry Design</a>
                    <a href="#">Beauty Consultation</a>
                    <a href="#">VIP Membership</a>
                </div>
                <div class="footer-section">
                    <h3>Boutique Hours</h3>
                    <p>Monday - Sunday</p>
                    <p>10:00 AM - 8:00 PM</p>
                    <p>Private appointments available</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LuxeCollection. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Luxury-specific interactions
        function openChat() {
            alert('Personal Concierge Service coming soon! For immediate luxury assistance, please call +91 98765 43210.');
        }

        // Add to collection with luxury feedback
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productName = this.closest('.product-card').querySelector('.product-name').textContent;
                alert(`${productName} added to your luxury collection! 💎`);

                this.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
                this.textContent = 'Added to Collection ✨';
                setTimeout(() => {
                    this.style.background = '';
                    this.textContent = 'Add to Collection';
                }, 3000);
            });
        });

        // Category interactions
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const categoryName = this.querySelector('.category-name').textContent;
                alert(`Exploring ${categoryName}... 👑`);
            });
        });

        // Gallery interactions
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                alert('Luxury gallery showcase coming soon! ✨');
            });
        });

        // Search functionality
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchTerm = document.querySelector('.search-box input').value;
            if (searchTerm.trim()) {
                alert(`Searching luxury collection for "${searchTerm}"...`);
            } else {
                alert('Please enter what luxury item you\'re looking for!');
            }
        });

        // Mobile menu interactions
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Luxury hover effects
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>

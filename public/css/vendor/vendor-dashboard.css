/* ===== MODERN VENDOR DASHBOARD 2025 ===== */
/* Ultra Clean & Professional Design - DESKTOP ONLY */

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* Hide mobile content on desktop */
.mobile-dashboard-app {
    display: none !important;
}

/* ===== DESKTOP STYLES ===== */
:root {
    /* Modern Color Palette */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --accent: #06b6d4;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Neutral Palette */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
    --transition-slow: all 0.3s ease;
    
    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
}

/* === WELCOME SECTION === */
.welcome-section {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.05) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: var(--shadow-sm);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -30%;
    right: -10%;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    border-radius: 50%;
    opacity: 0.06;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.welcome-content {
    position: relative;
    z-index: 2;
}

.welcome-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--space-2) 0;
    line-height: 1.3;
}

.welcome-subtitle {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin: 0;
}

/* === STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stats-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(20px);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.stats-card.sales { --stats-color: var(--primary); }
.stats-card.orders { --stats-color: var(--accent); }
.stats-card.products { --stats-color: var(--success); }
.stats-card.customers { --stats-color: var(--warning); }

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
    background: var(--stats-color);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    backdrop-filter: blur(10px);
}

.stats-trend.up {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.stats-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.stats-value {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1;
    font-family: var(--font-mono);
}

.stats-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* === DASHBOARD LAYOUT === */
.dashboard-section {
    margin-bottom: var(--space-8);
}

.dashboard-section:last-child {
    margin-bottom: 0;
}

/* === ORDERS SECTION === */
.orders-section {
    background: var(--white);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(20px);
}

.section-header {
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.section-action {
    color: var(--primary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid transparent;
}

.section-action:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* === QUICK ACTIONS === */
.quick-actions-section {
    background: var(--white);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--gray-200);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(20px);
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    background: var(--gray-50);
    text-decoration: none;
    color: var(--gray-900);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
    background: var(--white);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 1;
    overflow: hidden;
    background: var(--primary);
}

.quick-action-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.quick-action-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
    line-height: 1.3;
}

.quick-action-desc {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: 1.4;
    font-weight: 400;
}

/* === EMPTY STATE === */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-8);
    color: var(--gray-600);
}

.empty-icon {
    font-size: var(--font-size-4xl);
    color: var(--gray-400);
    margin-bottom: var(--space-4);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-2) 0;
}

.empty-state p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin: 0 0 var(--space-6) 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--primary);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: var(--transition);
}

.empty-state .btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
}

/* End of desktop styles */

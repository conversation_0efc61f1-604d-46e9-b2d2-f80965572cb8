/* ===== NATIVE MOBILE APP DASHBOARD ===== */
/* iOS/Android Style Design with Green Theme - Same as Orders Page */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    .mobile-dashboard-app {
        display: none !important;
    }
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;

    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;

    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 9999px;

    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;

    /* Native Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }

    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--background);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* ===== NATIVE APP LAYOUT ===== */
    .mobile-dashboard-app {
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }

    /* Page Container - Remove all padding */
    .vendor-content {
        background: var(--background);
        min-height: 100vh;
        padding: 0 !important;
        margin: 0 !important;
        overflow-x: hidden;
        position: relative;
    }

    /* Remove page content padding on mobile */
    .page-content {
        padding: 0 !important;
        margin: 0 !important;
        background: var(--background);
    }

    /* Target vendor main wrapper */
    .vendor-main {
        padding: 0 !important;
        margin: 0 !important;
    }
    
    /* ===== NATIVE WELCOME SECTION ===== */
    .mobile-welcome-section {
        background: var(--white);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
    }

    .mobile-welcome-title {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.3;
    }

    .mobile-welcome-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
    }

    /* Legacy welcome section support */
    .welcome-section {
        background: var(--white);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        border-radius: 0;
        box-shadow: none;
        backdrop-filter: none;
    }

    .welcome-title {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.3;
    }

    .welcome-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
    }
    
    /* ===== NATIVE STATS CARDS ===== */
    .mobile-stats-section {
        padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }

    .mobile-stats-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .mobile-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    .mobile-stat-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
    }

    .mobile-stat-card:active {
        transform: scale(0.98);
    }

    .mobile-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }

    .mobile-stat-number {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }

    .mobile-stat-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    .mobile-stat-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
    }

    /* Status-specific gradients for dashboard stats */
    .mobile-stat-card.sales {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
    }

    .mobile-stat-card.orders {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }

    .mobile-stat-card.products {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }

    .mobile-stat-card.customers {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }

    /* Legacy stats grid support */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);
        background: var(--white);
        margin-bottom: var(--spacing-md);
    }

    .stats-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
        border: none;
    }

    .stats-card:active {
        transform: scale(0.98);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
    }

    .stats-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
        width: auto;
        height: auto;
        border-radius: 0;
        background: none;
        box-shadow: none;
    }

    .stats-trend {
        display: none; /* Hide trends in mobile for cleaner look */
    }

    .stats-value {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
        line-height: 1.1;
    }

    .stats-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
        text-transform: none;
        letter-spacing: normal;
    }

    /* Status-specific gradients for legacy stats cards */
    .stats-card.sales {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
    }

    .stats-card.orders {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }

    .stats-card.products {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }

    .stats-card.customers {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    /* ===== NATIVE DASHBOARD SECTIONS ===== */
    .mobile-dashboard-section {
        background: var(--white);
        margin-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
    }

    .mobile-section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .mobile-section-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .mobile-section-action {
        background: var(--surface-secondary);
        border: none;
        border-radius: var(--radius-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
        color: var(--primary);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .mobile-section-action:active {
        transform: scale(0.96);
        background: var(--primary-bg);
    }

    /* Legacy dashboard sections support */
    .dashboard-section {
        background: var(--white);
        margin-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
    }

    .orders-section, .quick-actions-section {
        background: var(--white);
        margin: 0 0 var(--spacing-md) 0;
        border-radius: 0;
        box-shadow: none;
        border: none;
        border-bottom: 1px solid var(--border-light);
        overflow: visible;
    }

    .section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: transparent;
    }

    .section-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .section-action {
        background: var(--surface-secondary);
        border: none;
        border-radius: var(--radius-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
        color: var(--primary);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .section-action:active {
        transform: scale(0.96);
        background: var(--primary-bg);
    }
    
    /* ===== NATIVE QUICK ACTIONS ===== */
    .mobile-quick-actions {
        padding: 0 var(--spacing-sm) var(--spacing-3xl);
    }

    .mobile-quick-actions-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
        padding: 0 var(--spacing-xs);
    }

    .mobile-quick-actions-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
    }

    .mobile-quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    .mobile-quick-action-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        text-decoration: none;
        color: inherit;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-height: 100px;
        justify-content: center;
    }

    .mobile-quick-action-card:active {
        transform: translateY(1px);
        box-shadow: var(--shadow-sm);
    }

    .mobile-quick-action-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        background: var(--primary-bg);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xl);
        margin-bottom: var(--spacing-md);
    }

    .mobile-quick-action-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.3;
    }

    .mobile-quick-action-desc {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        line-height: 1.4;
    }

    /* Legacy quick actions support */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .quick-action-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        text-decoration: none;
        color: inherit;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-height: 100px;
        justify-content: center;
        position: relative;
        overflow: visible;
    }

    .quick-action-card::before {
        display: none; /* Remove gradient line for cleaner look */
    }

    .quick-action-card:active {
        transform: translateY(1px);
        box-shadow: var(--shadow-sm);
    }

    .quick-action-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        background: var(--primary-bg);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xl);
        margin-bottom: var(--spacing-md);
        box-shadow: none;
    }

    .quick-action-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.3;
    }

    .quick-action-desc {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        line-height: 1.4;
    }
    
    /* ===== NATIVE EMPTY STATE ===== */
    .mobile-empty-state {
        padding: var(--spacing-3xl) var(--spacing-lg);
        text-align: center;
        background: var(--white);
    }

    .mobile-empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--surface-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-lg);
        font-size: var(--text-3xl);
        color: var(--text-tertiary);
    }

    .mobile-empty-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .mobile-empty-desc {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-2xl);
        line-height: 1.5;
    }

    .mobile-empty-action {
        background: var(--primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-2xl);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .mobile-empty-action:active {
        transform: scale(0.98);
        background: var(--primary-dark);
    }

    /* Legacy empty state support */
    .empty-state {
        padding: var(--spacing-3xl) var(--spacing-lg);
        text-align: center;
        background: var(--white);
    }

    .empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--surface-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-lg);
        font-size: var(--text-3xl);
        color: var(--text-tertiary);
    }

    .empty-state h3 {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .empty-state p {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-2xl);
        line-height: 1.5;
    }

    .empty-state .btn {
        background: var(--primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-2xl);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: none;
    }

    .empty-state .btn:active {
        transform: scale(0.98);
        background: var(--primary-dark);
    }

    /* ===== NATIVE LOADING STATES ===== */
    .mobile-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-3xl);
        background: var(--white);
    }

    .mobile-loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid var(--border-light);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: var(--spacing-lg);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mobile-loading-text {
        font-size: var(--text-base);
        color: var(--text-secondary);
        font-weight: 500;
    }
}

/* === SMALL MOBILE STYLES (max-width: 480px) === */
@media (max-width: 480px) {
    /* Optimize for very small screens */
    .mobile-stats-grid,
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .mobile-stat-card,
    .stats-card {
        padding: var(--spacing-md);
    }

    .mobile-stat-number,
    .stats-value {
        font-size: var(--text-xl);
    }

    .mobile-quick-actions-grid,
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .mobile-quick-action-card,
    .quick-action-card {
        flex-direction: row;
        text-align: left;
        padding: var(--spacing-md);
        min-height: 60px;
    }

    .mobile-quick-action-icon,
    .quick-action-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 0;
        margin-right: var(--spacing-md);
        font-size: var(--text-lg);
    }

    .mobile-quick-action-title,
    .quick-action-title {
        font-size: var(--text-sm);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-quick-action-desc,
    .quick-action-desc {
        font-size: var(--text-xs);
    }

    .mobile-section-header,
    .section-header {
        padding: var(--spacing-md);
    }

    .mobile-section-title,
    .section-title {
        font-size: var(--text-base);
    }
}

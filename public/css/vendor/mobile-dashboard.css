/* ===== NATIVE MOBILE APP DASHBOARD ===== */
/* iOS/Android Style Design with Green Theme */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    .mobile-dashboard-app {
        display: none !important;
    }
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;
    
    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    
    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Native Typography */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    
    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Native Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    
    /* Page Container - Remove all padding */
    .vendor-content {
        background: var(--background);
        min-height: 100vh;
        padding: 0 !important;
        margin: 0 !important;
        overflow-x: hidden;
        position: relative;
    }
    
    /* Remove page content padding on mobile */
    .page-content {
        padding: 0 !important;
        margin: 0 !important;
        background: var(--background);
    }
    
    /* Target vendor main wrapper */
    .vendor-main {
        padding: 0 !important;
        margin: 0 !important;
    }
    
    /* Mobile App-style Dashboard */
    .welcome-section {
        background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(102, 187, 106, 0.05) 100%);
        border-radius: var(--radius-xl);
        margin: 0 var(--spacing-lg) var(--spacing-2xl) var(--spacing-lg);
        padding: var(--spacing-2xl) var(--spacing-xl);
        border: none;
        box-shadow: 0 4px 20px rgba(126, 217, 87, 0.15);
        backdrop-filter: blur(20px);
    }
    
    .welcome-title {
        font-size: var(--text-2xl);
        line-height: 1.3;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
        font-weight: 700;
    }
    
    .welcome-subtitle {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
    }
    
    /* Mobile Stats Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        margin: 0 var(--spacing-lg) var(--spacing-2xl) var(--spacing-lg);
    }
    
    .stats-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid var(--border-light);
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }
    
    .stats-card:hover,
    .stats-card:active {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }
    
    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-lg);
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xl);
        color: var(--white);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
    }
    
    .stats-trend {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-xs);
        font-weight: 600;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        background: rgba(34, 197, 94, 0.1);
        color: var(--success);
    }
    
    .stats-value {
        font-size: var(--text-3xl);
        font-weight: 800;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.1;
    }
    
    .stats-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    /* Mobile Dashboard Sections */
    .dashboard-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .orders-section, .quick-actions-section {
        background: var(--white);
        border-radius: var(--radius-xl);
        margin: 0 var(--spacing-lg);
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid var(--border-light);
        overflow: hidden;
    }
    
    .section-header {
        padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .section-title {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }
    
    .section-action {
        color: var(--primary);
        font-weight: 600;
        text-decoration: none;
        font-size: var(--text-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-md);
        background: rgba(16, 185, 129, 0.1);
        transition: var(--transition-fast);
    }
    
    .section-action:hover {
        background: rgba(16, 185, 129, 0.2);
        transform: translateY(-1px);
    }
    
    /* Mobile Quick Actions */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-xl);
    }
    
    .quick-action-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl) var(--spacing-lg);
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid var(--border-light);
        text-decoration: none;
        color: inherit;
        transition: var(--transition-normal);
        min-height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .quick-action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .quick-action-card:hover,
    .quick-action-card:active {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    }
    
    .quick-action-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-lg);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-lg);
        margin-bottom: var(--spacing-md);
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
    }
    
    .quick-action-title {
        font-size: var(--text-base);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.2;
    }
    
    .quick-action-desc {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        line-height: 1.3;
    }
    
    /* Mobile Empty State */
    .empty-state {
        padding: var(--spacing-3xl) var(--spacing-xl);
        text-align: center;
    }
    
    .empty-icon {
        font-size: 3rem;
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-lg);
    }
    
    .empty-state h3 {
        font-size: var(--text-lg);
        font-weight: 700;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
    }
    
    .empty-state p {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
        line-height: 1.4;
    }
    
    .empty-state .btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-2xl);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        color: var(--white);
        text-decoration: none;
        border-radius: var(--radius-lg);
        font-weight: 600;
        font-size: var(--text-sm);
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        transition: var(--transition-normal);
    }
    
    .empty-state .btn:hover,
    .empty-state .btn:active {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(16, 185, 129, 0.4);
    }
}

/* === SMALL MOBILE STYLES (max-width: 480px) === */
@media (max-width: 480px) {
    /* Extra small screens optimization */
    .welcome-section {
        border-radius: var(--radius-lg);
        margin: 0 var(--spacing-md) var(--spacing-xl) var(--spacing-md);
        padding: var(--spacing-xl) var(--spacing-lg);
    }
    
    .welcome-title {
        font-size: var(--text-xl);
    }
    
    .stats-grid {
        gap: var(--spacing-md);
        margin: 0 var(--spacing-md) var(--spacing-xl) var(--spacing-md);
    }
    
    .stats-card {
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        min-height: 80px;
    }
    
    .stats-value {
        font-size: var(--text-2xl);
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .quick-action-card {
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        min-height: 80px;
        flex-direction: row;
        text-align: left;
        align-items: center;
        justify-content: flex-start;
    }
    
    .quick-action-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 0;
        margin-right: var(--spacing-lg);
        font-size: var(--text-base);
    }
    
    .quick-action-content {
        flex: 1;
    }
    
    .quick-action-title {
        font-size: var(--text-sm);
        margin-bottom: 2px;
    }
    
    .quick-action-desc {
        font-size: var(--text-xs);
    }
    
    .orders-section, .quick-actions-section {
        border-radius: var(--radius-lg);
        margin: 0 var(--spacing-md);
    }
    
    .section-header {
        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
    }
    
    .section-title {
        font-size: var(--text-lg);
    }
    
    .dashboard-section {
        margin-bottom: var(--spacing-xl);
    }
}

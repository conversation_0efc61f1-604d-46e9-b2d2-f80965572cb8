/* ===== NATIVE MOBILE DASHBOARD ===== */
/* Simple & Modern Native App Design */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    .mobile-dashboard-app {
        display: none !important;
    }
}

:root {
    /* Primary Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;

    /* Border Colors */
    --border-light: #f3f4f6;
    --border-medium: #e5e7eb;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;

    /* Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 28px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* ===== MOBILE STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--bg-secondary);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        margin: 0;
        padding: 0;
        -webkit-font-smoothing: antialiased;
    }

    /* Remove default padding */
    .vendor-content,
    .page-content,
    .vendor-main {
        padding: 0 !important;
        margin: 0 !important;
        background: var(--bg-secondary);
    }
    
    /* ===== WELCOME SECTION ===== */
    .welcome-section {
        background: var(--bg-primary);
        padding: var(--space-6) var(--space-4);
        margin-bottom: var(--space-2);
    }

    .welcome-title {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--space-2);
        line-height: 1.3;
    }

    .welcome-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.4;
    }
    
    /* ===== STATS CARDS ===== */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-3);
        padding: 0 var(--space-4) var(--space-4);
        background: var(--bg-secondary);
    }

    .stats-card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        padding: var(--space-5);
        border: 1px solid var(--border-light);
        transition: transform 0.2s ease;
        position: relative;
    }

    .stats-card:active {
        transform: scale(0.98);
    }

    .stats-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-4);
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-lg);
        color: var(--bg-primary);
    }

    .stats-card.sales .stats-icon {
        background: var(--primary);
    }

    .stats-card.orders .stats-icon {
        background: var(--info);
    }

    .stats-card.products .stats-icon {
        background: var(--success);
    }

    .stats-card.customers .stats-icon {
        background: var(--warning);
    }

    .stats-trend {
        display: flex;
        align-items: center;
        gap: var(--space-1);
        font-size: var(--text-xs);
        font-weight: 600;
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        background: rgba(16, 185, 129, 0.1);
        color: var(--success);
    }

    .stats-value {
        font-size: var(--text-2xl);
        font-weight: 800;
        color: var(--text-primary);
        margin-bottom: var(--space-1);
        line-height: 1.2;
    }

    .stats-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* ===== DASHBOARD SECTIONS ===== */
    .dashboard-section {
        margin-bottom: var(--space-2);
    }

    .orders-section,
    .quick-actions-section {
        background: var(--bg-primary);
        margin-bottom: var(--space-2);
    }

    .section-header {
        padding: var(--space-5) var(--space-4);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .section-title {
        font-size: var(--text-lg);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .section-action {
        background: var(--bg-tertiary);
        color: var(--primary);
        text-decoration: none;
        font-size: var(--text-sm);
        font-weight: 600;
        padding: var(--space-2) var(--space-3);
        border-radius: var(--radius-sm);
        transition: all 0.2s ease;
    }

    .section-action:active {
        transform: scale(0.96);
        background: rgba(16, 185, 129, 0.1);
    }
    
    /* ===== QUICK ACTIONS ===== */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-3);
        padding: var(--space-4);
    }

    .quick-action-card {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        text-decoration: none;
        color: inherit;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-height: 100px;
        justify-content: center;
    }

    .quick-action-card:active {
        transform: scale(0.98);
        background: var(--bg-tertiary);
    }

    .quick-action-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        background: rgba(16, 185, 129, 0.1);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xl);
        margin-bottom: var(--space-3);
    }

    .quick-action-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--space-1);
        line-height: 1.3;
    }

    .quick-action-desc {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.4;
    }
    
    /* ===== EMPTY STATE ===== */
    .empty-state {
        padding: var(--space-8) var(--space-4);
        text-align: center;
        background: var(--bg-primary);
    }

    .empty-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: var(--bg-tertiary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        font-size: var(--text-2xl);
        color: var(--text-tertiary);
    }

    .empty-state h3 {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--space-2);
    }

    .empty-state p {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin-bottom: var(--space-6);
        line-height: 1.5;
    }

    .empty-state .btn {
        background: var(--primary);
        color: var(--bg-primary);
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--space-3) var(--space-6);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
    }

    .empty-state .btn:active {
        transform: scale(0.98);
        background: var(--primary-dark);
    }
    

}

/* === SMALL MOBILE STYLES (max-width: 480px) === */
@media (max-width: 480px) {
    /* Single column layout for very small screens */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-2);
        padding: 0 var(--space-3) var(--space-3);
    }

    .stats-card {
        padding: var(--space-4);
    }

    .stats-value {
        font-size: var(--text-xl);
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--space-2);
        padding: var(--space-3);
    }

    .quick-action-card {
        flex-direction: row;
        text-align: left;
        padding: var(--space-3);
        min-height: 60px;
    }

    .quick-action-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 0;
        margin-right: var(--space-3);
        font-size: var(--text-lg);
    }

    .quick-action-title {
        font-size: var(--text-sm);
        margin-bottom: var(--space-1);
    }

    .quick-action-desc {
        font-size: var(--text-xs);
    }

    .section-header {
        padding: var(--space-3);
    }

    .section-title {
        font-size: var(--text-base);
    }

    .welcome-section {
        padding: var(--space-4) var(--space-3);
    }

    .welcome-title {
        font-size: var(--text-xl);
    }
}

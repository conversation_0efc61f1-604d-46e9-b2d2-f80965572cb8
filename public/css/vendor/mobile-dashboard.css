/* ===== NATIVE MOBILE APP DASHBOARD ===== */
/* iOS/Android Style Design with Green Theme - Same as Orders Page */

/* Hide desktop content on mobile */
.dashboard-container {
    display: none !important;
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;

    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;

    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;

    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE STYLES ===== */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
}

html, body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    font-size: var(--text-base);
    line-height: 1.5;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== NATIVE APP LAYOUT ===== */
.mobile-dashboard-app {
    min-height: 100vh;
    background: var(--background);
    padding-top: var(--safe-area-top);
    padding-bottom: var(--safe-area-bottom);
    position: relative;
}
    
/* ===== NATIVE STATS CARDS ===== */
.mobile-stats-section {
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);
    background: var(--white);
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

.mobile-stats-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.mobile-stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

.mobile-stat-card {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    color: var(--white);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-card);
    transition: transform 0.2s ease;
}

.mobile-stat-card:active {
    transform: scale(0.98);
}

.mobile-stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20px;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.mobile-stat-number {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.mobile-stat-label {
    font-size: var(--text-sm);
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.mobile-stat-icon {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    font-size: var(--text-xl);
    opacity: 0.8;
    z-index: 2;
}

/* Status-specific gradients */
.mobile-stat-card.orders {
    background: linear-gradient(135deg, var(--warning), #fbbf24);
}

.mobile-stat-card.products {
    background: linear-gradient(135deg, var(--info), #60a5fa);
}

.mobile-stat-card.customers {
    background: linear-gradient(135deg, var(--success), var(--primary-light));
}

/* ===== NATIVE QUICK ACTIONS ===== */
.mobile-quick-actions {
    background: var(--white);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.mobile-quick-actions-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.mobile-quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.mobile-quick-action-card {
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 100px;
    justify-content: center;
    border: 1px solid var(--border-light);
}

.mobile-quick-action-card:active {
    transform: scale(0.98);
    background: var(--primary-bg);
}

.mobile-quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    background: var(--primary-bg);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    margin-bottom: var(--spacing-md);
}

.mobile-quick-action-title {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
}

.mobile-quick-action-desc {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* ===== NATIVE RECENT ORDERS ===== */
.mobile-recent-orders {
    background: var(--white);
    margin-bottom: var(--spacing-md);
}

.mobile-section-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-section-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.mobile-section-action {
    background: var(--surface-secondary);
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    color: var(--primary);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-section-action:active {
    transform: scale(0.96);
    background: var(--primary-bg);
}
    
/* ===== NATIVE EMPTY STATE ===== */
.mobile-empty-state {
    padding: var(--spacing-3xl) var(--spacing-lg);
    text-align: center;
    background: var(--white);
}

.mobile-empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--surface-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--text-3xl);
    color: var(--text-tertiary);
}

.mobile-empty-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.mobile-empty-description {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.5;
}

.mobile-empty-action {
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.mobile-empty-action:active {
    transform: scale(0.98);
    background: var(--primary-dark);
}

/* ===== NATIVE SAFE AREA ===== */
.mobile-safe-bottom {
    height: calc(var(--safe-area-bottom) + var(--spacing-2xl));
    background: var(--background);
}
    

/* === SMALL MOBILE STYLES (max-width: 480px) === */
@media (max-width: 480px) {
    /* Smaller spacing for very small screens */
    .mobile-stats-grid {
        gap: var(--spacing-sm);
    }

    .mobile-stat-card {
        padding: var(--spacing-md);
    }

    .mobile-stat-number {
        font-size: var(--text-xl);
    }

    .mobile-quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .mobile-quick-action-card {
        flex-direction: row;
        text-align: left;
        padding: var(--spacing-md);
        min-height: 60px;
    }

    .mobile-quick-action-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 0;
        margin-right: var(--spacing-md);
        font-size: var(--text-lg);
    }

    .mobile-quick-action-title {
        font-size: var(--text-sm);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-quick-action-desc {
        font-size: var(--text-xs);
    }

    .mobile-section-header {
        padding: var(--spacing-md);
    }

    .mobile-section-title {
        font-size: var(--text-base);
    }
}
